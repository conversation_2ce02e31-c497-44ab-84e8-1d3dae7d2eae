<?php
// Include session management
include '../includes/session.php';

// Check if database connection has error
if (isset($db_error) && $db_error) {
    die('Database connection error occurred.');
}

// Check if user is admin
if (!isset($_SESSION['user_id']) || $_SESSION['user_type'] != 'Admin') {
    die('You do not have permission to access this page.');
}

echo "<h2>Creating test users...</h2>";

try {
    // Create test users for different roles
    $test_users = [
        [
            'username' => 'secretary_test',
            'password' => password_hash('password123', PASSWORD_DEFAULT),
            'user_type' => 'Secretary',
            'email' => '<EMAIL>',
            'status' => 'Active'
        ],
        [
            'username' => 'chairman_test',
            'password' => password_hash('password123', PASSWORD_DEFAULT),
            'user_type' => 'Chairman',
            'email' => '<EMAIL>',
            'status' => 'Active'
        ],
        [
            'username' => 'kagawad_test',
            'password' => password_hash('password123', PASSWORD_DEFAULT),
            'user_type' => 'Kagawad',
            'email' => '<EMAIL>',
            'status' => 'Active'
        ],
        [
            'username' => 'staff_test',
            'password' => password_hash('password123', PASSWORD_DEFAULT),
            'user_type' => 'Staff',
            'email' => '<EMAIL>',
            'status' => 'Active'
        ]
    ];
    
    $stmt = $conn->prepare("INSERT IGNORE INTO users (username, password, user_type, email, status, date_created) VALUES (?, ?, ?, ?, ?, NOW())");
    
    foreach ($test_users as $user) {
        $result = $stmt->execute([$user['username'], $user['password'], $user['user_type'], $user['email'], $user['status']]);
        if ($result) {
            echo "✓ Created user: {$user['username']} ({$user['user_type']})<br>";
        } else {
            echo "⚠ User {$user['username']} may already exist<br>";
        }
    }
    
    echo "<br><div style='color: green;'><strong>✓ Test users created successfully!</strong></div>";
    echo "<br><h3>Test User Credentials:</h3>";
    echo "<ul>";
    echo "<li><strong>Secretary:</strong> username: secretary_test, password: password123</li>";
    echo "<li><strong>Chairman:</strong> username: chairman_test, password: password123</li>";
    echo "<li><strong>Kagawad:</strong> username: kagawad_test, password: password123</li>";
    echo "<li><strong>Staff:</strong> username: staff_test, password: password123</li>";
    echo "</ul>";
    
    echo "<br><p><strong>Instructions:</strong></p>";
    echo "<ol>";
    echo "<li>Logout from admin account</li>";
    echo "<li>Login with one of the test accounts above</li>";
    echo "<li>Check the sidebar - you should only see modules/features you have permissions for</li>";
    echo "<li>Try accessing different pages to test permission restrictions</li>";
    echo "</ol>";
    
    echo "<br><a href='../logout.php'>Logout</a> | <a href='test_permissions.php'>Test Permissions</a> | <a href='permissions.php'>Manage Permissions</a>";
    
} catch (PDOException $e) {
    echo "<div style='color: red;'>Error: " . $e->getMessage() . "</div>";
}
?>
