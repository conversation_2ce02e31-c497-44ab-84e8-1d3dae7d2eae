<?php
// Include session management
include '../includes/session.php';

// Check if database connection has error
if (isset($db_error) && $db_error) {
    die('Database connection error occurred.');
}

// Check if user is admin
if (!isset($_SESSION['user_id']) || $_SESSION['user_type'] != 'Admin') {
    die('You do not have permission to access this page.');
}

echo "<h2>Setting up test permissions...</h2>";

try {
    // First, let's add some basic permissions to the permissions table
    $basic_permissions = [
        // Dashboard permissions
        ['role_id' => 1, 'module' => 'dashboard', 'permission_name' => 'view_dashboard', 'description' => 'View Dashboard'],
        ['role_id' => 2, 'module' => 'dashboard', 'permission_name' => 'view_dashboard', 'description' => 'View Dashboard'],
        ['role_id' => 3, 'module' => 'dashboard', 'permission_name' => 'view_dashboard', 'description' => 'View Dashboard'],
        ['role_id' => 4, 'module' => 'dashboard', 'permission_name' => 'view_dashboard', 'description' => 'View Dashboard'],
        
        // Residents permissions
        ['role_id' => 1, 'module' => 'residents', 'permission_name' => 'view_residents', 'description' => 'View Residents'],
        ['role_id' => 1, 'module' => 'residents', 'permission_name' => 'add_resident', 'description' => 'Add New Resident'],
        ['role_id' => 2, 'module' => 'residents', 'permission_name' => 'view_residents', 'description' => 'View Residents'],
        ['role_id' => 3, 'module' => 'residents', 'permission_name' => 'view_residents', 'description' => 'View Residents'],
        ['role_id' => 4, 'module' => 'residents', 'permission_name' => 'view_residents', 'description' => 'View Residents'],
        
        // Documents permissions
        ['role_id' => 1, 'module' => 'documents', 'permission_name' => 'view_documents', 'description' => 'View Document Requests'],
        ['role_id' => 1, 'module' => 'documents', 'permission_name' => 'issue_clearance', 'description' => 'Issue Barangay Clearance'],
        ['role_id' => 1, 'module' => 'documents', 'permission_name' => 'issue_certificate', 'description' => 'Issue Certificates'],
        ['role_id' => 4, 'module' => 'documents', 'permission_name' => 'view_documents', 'description' => 'View Document Requests'],
        
        // Reports permissions
        ['role_id' => 2, 'module' => 'reports', 'permission_name' => 'view_reports', 'description' => 'View Reports'],
        ['role_id' => 3, 'module' => 'reports', 'permission_name' => 'view_reports', 'description' => 'View Reports'],
        
        // Complaints permissions
        ['role_id' => 2, 'module' => 'complaints', 'permission_name' => 'view_complaints', 'description' => 'View Complaints'],
        ['role_id' => 2, 'module' => 'complaints', 'permission_name' => 'manage_hearings', 'description' => 'Manage Hearings'],
    ];
    
    // Insert permissions
    $stmt = $conn->prepare("INSERT IGNORE INTO permissions (role_id, module, permission_name, description, status) VALUES (?, ?, ?, ?, 'active')");
    
    foreach ($basic_permissions as $perm) {
        $stmt->execute([$perm['role_id'], $perm['module'], $perm['permission_name'], $perm['description']]);
        echo "Added permission: {$perm['permission_name']} for role {$perm['role_id']} in module {$perm['module']}<br>";
    }
    
    echo "<br><h3>Now setting up user_permissions assignments...</h3>";
    
    // Get all permission IDs and assign them to roles
    $stmt = $conn->query("SELECT permission_id, role_id, module, permission_name FROM permissions");
    $permissions = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    // Insert into user_permissions table
    $stmt = $conn->prepare("INSERT IGNORE INTO user_permissions (role_id, permission_id) VALUES (?, ?)");
    
    foreach ($permissions as $perm) {
        $stmt->execute([$perm['role_id'], $perm['permission_id']]);
        echo "Assigned permission {$perm['permission_name']} (ID: {$perm['permission_id']}) to role {$perm['role_id']}<br>";
    }
    
    echo "<br><div style='color: green;'><strong>✓ Test permissions setup completed successfully!</strong></div>";
    echo "<br><a href='test_permissions.php'>Test Permissions</a> | <a href='permissions.php'>Manage Permissions</a>";
    
} catch (PDOException $e) {
    echo "<div style='color: red;'>Error: " . $e->getMessage() . "</div>";
}
?>
