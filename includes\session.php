<?php
// Check if session file has already been included
if (defined('SESSION_INCLUDED')) {
    return;
}

// Mark this file as included
define('SESSION_INCLUDED', true);

// Enable error reporting for debugging
error_reporting(E_ALL);
ini_set('display_errors', 1);

// Function to set security headers - with existence check
if (!function_exists('set_security_headers')) {
    function set_security_headers() {
        if (!headers_sent()) {
            header("X-Content-Type-Options: nosniff");
            header("X-Frame-Options: SAMEORIGIN");
            header("X-XSS-Protection: 1; mode=block");
            header("Referrer-Policy: strict-origin-when-cross-origin");
        }
    }
}

// Set security headers immediately
set_security_headers();

// Only start session if one isn't already active
if (session_status() === PHP_SESSION_NONE) {
    // Set secure session parameters before starting the session
    ini_set('session.cookie_httponly', 1); // Prevent JavaScript access to session cookie
    ini_set('session.use_only_cookies', 1); // Only use cookies for sessions
    ini_set('session.cookie_secure', isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on'); // Secure cookies when HTTPS is on
    ini_set('session.gc_maxlifetime', 3600); // Set session timeout to 1 hour (adjust based on requirements)

    // Now start the session
    session_start();
}

// Check for session timeout
$session_timeout = 3600; // Default 1 hour (in seconds)
if (isset($_SESSION['system_settings']) && isset($_SESSION['system_settings']['session_timeout'])) {
    $session_timeout = (int)$_SESSION['system_settings']['session_timeout'];
}

if (isset($_SESSION['last_activity']) && (time() - $_SESSION['last_activity'] > $session_timeout)) {
    // Session has expired, destroy it
    session_unset();
    session_destroy();

    // Create a new session
    session_start();

    // Set message using ErrorHandler if available, otherwise fallback
    if (class_exists('ErrorHandler')) {
        ErrorHandler::setMessage("Your session has expired. Please log in again.", ErrorHandler::ERROR);
    } else {
        $_SESSION['error'] = "Your session has expired. Please log in again.";
    }

    // Set redirect flag
    $_SESSION['session_expired'] = true;
}

// Update last activity time
$_SESSION['last_activity'] = time();

// Define utility function here (used later for login redirect)
if (!function_exists('get_base_url')) {
    function get_base_url() {
        $protocol = isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on' ? 'https' : 'http';
        $host = $_SERVER['HTTP_HOST'];

        // Get directory without the file
        $uri_parts = explode('/', $_SERVER['REQUEST_URI']);

        // Find 'barangay' in the URI parts
        $path = '';
        foreach($uri_parts as $part) {
            $path .= $part . '/';
            if($part == 'barangay') {
                break;
            }
        }

        return $protocol . '://' . $host . $path;
    }
}

// Include class autoloader
include_once __DIR__ . '/autoload.php';

// Include legacy database configuration for backward compatibility
include_once __DIR__ . '/config/database.php';

// Include utility functions
include_once __DIR__ . '/functions/utility.php';

// Include permission functions
include_once __DIR__ . '/functions/permission_functions.php';

// Initialize session debugging
if (!isset($_SESSION['debug'])) {
    $_SESSION['debug'] = [];
}

// Add timestamp to debug log
$_SESSION['debug'][] = "Session accessed: " . date('Y-m-d H:i:s');

// Check for session expiration redirect
if (isset($_SESSION['session_expired']) && $_SESSION['session_expired'] === true) {
    // Clear flag
    unset($_SESSION['session_expired']);

    // Only redirect if not already on the login page
    if (!strpos($_SERVER['PHP_SELF'], 'login.php')) {
        if (!headers_sent()) {
            header("Location: " . get_base_url() . "login.php");
            exit();
        } else {
            echo "<script>window.location.href='" . get_base_url() . "login.php';</script>";
            exit();
        }
    }
}

// Check if user is not logged in and we're not already on login page
if (!isset($_SESSION['user_id']) && !strpos($_SERVER['PHP_SELF'], 'login.php')) {
    // Store original requested URL for redirect after login
    $_SESSION['requested_page'] = $_SERVER['REQUEST_URI'];

    // Add debug information
    $_SESSION['debug'][] = "User not logged in, redirecting to login page";

    // Determine the correct login path
    $login_path = get_base_url() . 'login.php';

    if (!headers_sent()) {
        header("Location: $login_path");
        exit();
    } else {
        echo "<script>window.location.href='$login_path';</script>";
        exit();
    }
}

// Make sure $user_type variable is always defined
if (!isset($_SESSION['user_type'])) {
    $_SESSION['user_type'] = '';
}

// Make sure $user_role variable is always defined
if (!isset($_SESSION['role'])) {
    $_SESSION['role'] = '';
}

// Initialize database connection using the new Database class if available
try {
    if (class_exists('Database')) {
        $db = Database::getInstance();
        $conn = $db->getConnection();
        $db_connection_error = $db->hasError();
        $db_error = $db->hasError(); // For backward compatibility

        // Store last successful connection time
        if (!$db_connection_error) {
            $_SESSION['db_last_connect'] = date('Y-m-d H:i:s');
        }
    } else {
        // Fallback to legacy connection if Database class is not available
        // This should already be handled by the included database.php
    }
} catch(Exception $e) {
    if (class_exists('ErrorHandler')) {
        ErrorHandler::handleException($e, false);
    } else {
        $db_connection_error = true;
        $db_error = true;
        $_SESSION['error'] = "Connection failed: " . $e->getMessage();
        $_SESSION['debug'][] = "Database connection error: " . $e->getMessage();
    }
}

// If user is logged in, check if we need to update session with more user information
if (isset($_SESSION['user_id']) && !isset($_SESSION['user_info_loaded']) && !$db_connection_error) {
    try {
        // Get full user information
        $stmt = $conn->prepare("SELECT * FROM users WHERE user_id = :user_id");
        $stmt->bindParam(':user_id', $_SESSION['user_id'], PDO::PARAM_INT);
        $stmt->execute();

        if ($user = $stmt->fetch()) {
            // Store additional user info in session
            $_SESSION['username'] = $user['username'];
            $_SESSION['user_type'] = $user['user_type'];
            $_SESSION['role'] = $user['user_type']; // Set role as user_type for compatibility
            $_SESSION['email'] = $user['email'];
            $_SESSION['user_info_loaded'] = true;

            $_SESSION['debug'][] = "User information loaded for user ID: " . $_SESSION['user_id'];
        }
    } catch(PDOException $e) {
        if (class_exists('ErrorHandler')) {
            ErrorHandler::logError("Error loading user information: " . $e->getMessage(), 'ERROR', __FILE__, __LINE__);
        } else {
            $_SESSION['debug'][] = "Error loading user information: " . $e->getMessage();
        }
    }
}