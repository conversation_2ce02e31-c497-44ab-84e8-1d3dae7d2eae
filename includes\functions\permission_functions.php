<?php
/**
 * Permission Functions
 * Functions for checking user permissions
 */

/**
 * Check if user has a specific permission
 * Admin users have all permissions by default
 * Other user types are checked against the database using role-based permissions
 */
function hasPermission($permission_name, $module = null, $conn = null) {
    // Check if user is logged in
    if (!isset($_SESSION['user_id']) || !isset($_SESSION['user_type'])) {
        return false;
    }

    $user_type = $_SESSION['user_type'];

    // Admin has all permissions
    if ($user_type === 'Admin') {
        return true;
    }

    // For other user types, check database using role-based system
    if ($conn === null) {
        global $conn;
        if (!$conn) {
            return false;
        }
    }

    try {
        // Get role_id for user type
        $role_id = getUserRoleId($user_type, $conn);
        if (!$role_id) {
            return false;
        }

        if ($module) {
            // Check specific module permission using role-based system
            $stmt = $conn->prepare("
                SELECT COUNT(*)
                FROM permissions p
                INNER JOIN user_permissions up ON p.permission_id = up.permission_id
                WHERE up.role_id = ? AND p.module = ? AND p.permission_name = ? AND p.status = 'active'
            ");
            $stmt->execute([$role_id, $module, $permission_name]);
        } else {
            // Check permission without module using role-based system
            $stmt = $conn->prepare("
                SELECT COUNT(*)
                FROM permissions p
                INNER JOIN user_permissions up ON p.permission_id = up.permission_id
                WHERE up.role_id = ? AND p.permission_name = ? AND p.status = 'active'
            ");
            $stmt->execute([$role_id, $permission_name]);
        }

        return $stmt->fetchColumn() > 0;
    } catch (Exception $e) {
        error_log("Permission check error: " . $e->getMessage());
        return false;
    }
}

/**
 * Get role_id for a user type
 */
function getUserRoleId($user_type, $conn = null) {
    if ($conn === null) {
        global $conn;
        if (!$conn) {
            return false;
        }
    }

    // Map user types to role IDs (based on the structure we created)
    $role_mapping = [
        'Secretary' => 1,
        'Chairman' => 2,
        'Kagawad' => 3,
        'Staff' => 4
    ];

    return isset($role_mapping[$user_type]) ? $role_mapping[$user_type] : false;
}

/**
 * Check if user can access a specific module
 */
function canAccessModule($module, $conn = null) {
    // Check if user is logged in
    if (!isset($_SESSION['user_id']) || !isset($_SESSION['user_type'])) {
        return false;
    }

    $user_type = $_SESSION['user_type'];

    // Admin can access all modules
    if ($user_type === 'Admin') {
        return true;
    }

    // For other user types, check if they have any permission in the module using role-based system
    if ($conn === null) {
        global $conn;
        if (!$conn) {
            return false;
        }
    }

    try {
        // Get role_id for user type
        $role_id = getUserRoleId($user_type, $conn);
        if (!$role_id) {
            return false;
        }

        $stmt = $conn->prepare("
            SELECT COUNT(*)
            FROM permissions p
            INNER JOIN user_permissions up ON p.permission_id = up.permission_id
            WHERE up.role_id = ? AND p.module = ? AND p.status = 'active'
        ");
        $stmt->execute([$role_id, $module]);

        return $stmt->fetchColumn() > 0;
    } catch (Exception $e) {
        error_log("Module access check error: " . $e->getMessage());
        return false;
    }
}

/**
 * Get all permissions for a user type
 */
function getUserPermissions($user_type, $conn = null) {
    // Admin has all permissions
    if ($user_type === 'Admin') {
        return 'all'; // Special value indicating all permissions
    }
    
    if ($conn === null) {
        global $conn;
        if (!$conn) {
            return [];
        }
    }
    
    try {
        $stmt = $conn->prepare("SELECT module, permission_name FROM permissions WHERE user_type = ? AND status = 'active'");
        $stmt->execute([$user_type]);
        
        $permissions = [];
        while ($row = $stmt->fetch(PDO::FETCH_ASSOC)) {
            $permissions[$row['module']][] = $row['permission_name'];
        }
        
        return $permissions;
    } catch (Exception $e) {
        error_log("Get user permissions error: " . $e->getMessage());
        return [];
    }
}

/**
 * Check if current user is admin
 */
function isAdmin() {
    return isset($_SESSION['user_type']) && $_SESSION['user_type'] === 'Admin';
}

/**
 * Get user type display name
 */
function getUserTypeDisplayName($user_type) {
    $display_names = [
        'Admin' => 'Administrator',
        'Secretary' => 'Secretary',
        'Chairman' => 'Barangay Chairman',
        'Kagawad' => 'Barangay Kagawad',
        'Staff' => 'Staff Member'
    ];
    
    return isset($display_names[$user_type]) ? $display_names[$user_type] : $user_type;
}

/**
 * Redirect if user doesn't have permission
 */
function requirePermission($permission_name, $module = null, $redirect_url = '../index.php') {
    if (!hasPermission($permission_name, $module)) {
        $_SESSION['error'] = 'You do not have permission to access this page.';
        header("Location: $redirect_url");
        exit;
    }
}

/**
 * Redirect if user is not admin
 */
function requireAdmin($redirect_url = '../index.php') {
    if (!isAdmin()) {
        $_SESSION['error'] = 'You do not have permission to access this page.';
        header("Location: $redirect_url");
        exit;
    }
}

/**
 * Get accessible modules for current user
 */
function getAccessibleModules($conn = null) {
    if (!isset($_SESSION['user_type'])) {
        return [];
    }
    
    $user_type = $_SESSION['user_type'];
    
    // Admin can access all modules
    if ($user_type === 'Admin') {
        return [
            'dashboard', 'residents', 'households', 'officials', 'documents', 
            'complaints', 'social', 'health', 'finance', 'properties', 
            'projects', 'governance', 'disaster', 'events', 'reports', 
            'settings', 'users', 'admin'
        ];
    }
    
    if ($conn === null) {
        global $conn;
        if (!$conn) {
            return [];
        }
    }
    
    try {
        // Get role_id for user type
        $role_id = getUserRoleId($user_type, $conn);
        if (!$role_id) {
            return [];
        }

        $stmt = $conn->prepare("
            SELECT DISTINCT p.module
            FROM permissions p
            INNER JOIN user_permissions up ON p.permission_id = up.permission_id
            WHERE up.role_id = ? AND p.status = 'active'
        ");
        $stmt->execute([$role_id]);

        return $stmt->fetchAll(PDO::FETCH_COLUMN);
    } catch (Exception $e) {
        error_log("Get accessible modules error: " . $e->getMessage());
        return [];
    }
}

/**
 * Get all permissions for current user
 */
function getUserPermissions($conn = null) {
    // Check if user is logged in
    if (!isset($_SESSION['user_id']) || !isset($_SESSION['user_type'])) {
        return [];
    }

    $user_type = $_SESSION['user_type'];

    // Admin has all permissions
    if ($user_type === 'Admin') {
        return 'all'; // Special value indicating all permissions
    }

    // For other user types, get permissions from database using role-based system
    if ($conn === null) {
        global $conn;
        if (!$conn) {
            return [];
        }
    }

    try {
        // Get role_id for user type
        $role_id = getUserRoleId($user_type, $conn);
        if (!$role_id) {
            return [];
        }

        $stmt = $conn->prepare("
            SELECT p.module, p.permission_name
            FROM permissions p
            INNER JOIN user_permissions up ON p.permission_id = up.permission_id
            WHERE up.role_id = ? AND p.status = 'active'
        ");
        $stmt->execute([$role_id]);

        $permissions = [];
        while ($row = $stmt->fetch(PDO::FETCH_ASSOC)) {
            $permissions[$row['module']][] = $row['permission_name'];
        }

        return $permissions;
    } catch (Exception $e) {
        error_log("Get user permissions error: " . $e->getMessage());
        return [];
    }
}

/**
 * Check if user can perform CRUD operations
 */
function canCreate($module, $conn = null) {
    return hasPermission('add_' . substr($module, 0, -1), $module, $conn) || 
           hasPermission('create_' . substr($module, 0, -1), $module, $conn);
}

function canEdit($module, $conn = null) {
    return hasPermission('edit_' . substr($module, 0, -1), $module, $conn) || 
           hasPermission('update_' . substr($module, 0, -1), $module, $conn);
}

function canDelete($module, $conn = null) {
    return hasPermission('delete_' . substr($module, 0, -1), $module, $conn);
}

function canView($module, $conn = null) {
    return hasPermission('view_' . $module, $module, $conn);
}

?>
