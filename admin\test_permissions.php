<?php
// Include session management
include '../includes/session.php';

// Check if database connection has error
if (isset($db_error) && $db_error) {
    $_SESSION['error'] = 'Database connection error occurred. Please try again later.';
    header('location: ../index.php');
    exit();
}

// Check if user is logged in
if (!isset($_SESSION['user_id'])) {
    $_SESSION['error'] = 'You must be logged in to access this page.';
    header('location: ../login.php');
    exit();
}

// Set page title
$page_title = "Permission Test - Admin Panel";

// Include header
include '../includes/header.php';
?>

<div class="container-fluid">
    <div class="row">
        <!-- Main Content -->
        <main class="col-md-12 px-md-4">
            <!-- Content Header -->
            <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                <h1 class="h2">🔐 Permission Test</h1>
                <div class="btn-toolbar mb-2 mb-md-0">
                    <div class="btn-group">
                        <a href="permissions.php" class="btn btn-outline-secondary">
                            ← Back to Permissions
                        </a>
                    </div>
                </div>
            </div>

            <!-- User Info -->
            <div class="row mb-4">
                <div class="col-md-6">
                    <div class="card">
                        <div class="card-header">
                            <h5>Current User Information</h5>
                        </div>
                        <div class="card-body">
                            <p><strong>User ID:</strong> <?php echo $_SESSION['user_id']; ?></p>
                            <p><strong>Username:</strong> <?php echo $_SESSION['username'] ?? 'N/A'; ?></p>
                            <p><strong>User Type:</strong> <?php echo $_SESSION['user_type']; ?></p>
                            <p><strong>Role ID:</strong> <?php echo getUserRoleId($_SESSION['user_type'], $conn) ?: 'N/A'; ?></p>
                        </div>
                    </div>
                </div>
                
                <div class="col-md-6">
                    <div class="card">
                        <div class="card-header">
                            <h5>Permission Functions Test</h5>
                        </div>
                        <div class="card-body">
                            <p><strong>Is Admin:</strong> <?php echo isAdmin() ? 'Yes' : 'No'; ?></p>
                            <p><strong>Can Access Documents:</strong> <?php echo canAccessModule('documents', $conn) ? 'Yes' : 'No'; ?></p>
                            <p><strong>Can View Residents:</strong> <?php echo hasPermission('view_residents', 'residents', $conn) ? 'Yes' : 'No'; ?></p>
                            <p><strong>Can Issue Clearance:</strong> <?php echo hasPermission('issue_clearance', 'documents', $conn) ? 'Yes' : 'No'; ?></p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Accessible Modules -->
            <div class="row mb-4">
                <div class="col-md-12">
                    <div class="card">
                        <div class="card-header">
                            <h5>Accessible Modules</h5>
                        </div>
                        <div class="card-body">
                            <?php
                            $accessible_modules = getAccessibleModules($conn);
                            if (!empty($accessible_modules)) {
                                echo '<div class="row">';
                                foreach ($accessible_modules as $module) {
                                    echo '<div class="col-md-3 mb-2">';
                                    echo '<span class="badge bg-success">' . ucfirst($module) . '</span>';
                                    echo '</div>';
                                }
                                echo '</div>';
                            } else {
                                echo '<p class="text-muted">No accessible modules found.</p>';
                            }
                            ?>
                        </div>
                    </div>
                </div>
            </div>

            <!-- User Permissions -->
            <div class="row">
                <div class="col-md-12">
                    <div class="card">
                        <div class="card-header">
                            <h5>User Permissions</h5>
                        </div>
                        <div class="card-body">
                            <?php
                            $user_permissions = getUserPermissions($conn);
                            
                            if ($user_permissions === 'all') {
                                echo '<div class="alert alert-info">Admin user has all permissions.</div>';
                            } elseif (!empty($user_permissions)) {
                                echo '<div class="table-responsive">';
                                echo '<table class="table table-striped">';
                                echo '<thead><tr><th>Module</th><th>Permissions</th></tr></thead>';
                                echo '<tbody>';
                                
                                foreach ($user_permissions as $module => $permissions) {
                                    echo '<tr>';
                                    echo '<td><strong>' . ucfirst($module) . '</strong></td>';
                                    echo '<td>';
                                    foreach ($permissions as $permission) {
                                        echo '<span class="badge bg-primary me-1">' . $permission . '</span>';
                                    }
                                    echo '</td>';
                                    echo '</tr>';
                                }
                                
                                echo '</tbody>';
                                echo '</table>';
                                echo '</div>';
                            } else {
                                echo '<div class="alert alert-warning">No permissions found for this user.</div>';
                            }
                            ?>
                        </div>
                    </div>
                </div>
            </div>
        </main>
    </div>
</div>

<?php include '../includes/footer.php'; ?>
